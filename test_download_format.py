#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下载当前数据的格式是否与模板一致
"""

import os
import sys
import tempfile
import pandas as pd

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data.manager import MaterialsDataManager


def test_download_format_consistency():
    """测试下载数据格式与模板的一致性"""
    print("开始测试下载数据格式...")
    
    # 创建临时数据库
    temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
    temp_db.close()
    
    try:
        # 创建数据管理器并加载默认数据
        manager = MaterialsDataManager(temp_db.name)
        
        # 获取当前数据
        materials_data = manager.get_materials_data()
        print(f"获取到 {len(materials_data)} 条数据")
        
        if not materials_data:
            print("❌ 没有数据可测试")
            return
        
        # 转换为DataFrame
        df = pd.DataFrame(materials_data)
        print(f"原始列名: {list(df.columns)}")
        
        # 定义UI列名到模板列名的映射
        ui_to_template_mapping = {
            'Z': 'atomic_number',
            'Element': 'element',
            'Formula': 'formula',
            'Ratio': 'ratio',
            'Excess': 'excess',
            'Mr': 'molecular_weight',
            'CAS': 'cas',
            'Co.': 'company',
            'Product No.': 'product_no',
            'Purity': 'purity',
            'Pricing': 'pricing'
        }

        # 只保留UI列名，移除所有其他字段（包括数据库字段和内部字段）
        ui_columns = list(ui_to_template_mapping.keys())
        available_ui_columns = [col for col in ui_columns if col in df.columns]
        df = df[available_ui_columns]
        print(f"保留UI列后: {list(df.columns)}")

        # 将UI列名重命名为模板格式
        df = df.rename(columns=ui_to_template_mapping)
        print(f"转换后列名: {list(df.columns)}")

        # 确保列的顺序与模板一致
        template_columns = ['atomic_number', 'element', 'formula', 'ratio', 'excess',
                          'molecular_weight', 'cas', 'company', 'product_no', 'purity', 'pricing']

        # 按模板顺序重新排列列，如果某列不存在则填充NaN
        df = df.reindex(columns=template_columns)
        print(f"最终列名: {list(df.columns)}")
        
        # 读取模板文件获取期望的列名
        template_df = pd.read_excel('oxide_powders_template.xlsx')
        template_cols = list(template_df.columns)
        print(f"模板列名: {template_cols}")
        
        # 验证列名是否一致
        if list(df.columns) == template_cols:
            print("✅ 列名与模板完全一致！")
        else:
            print("❌ 列名与模板不一致")
            print(f"期望: {template_cols}")
            print(f"实际: {list(df.columns)}")
            return False
        
        # 验证列的顺序
        for i, (expected, actual) in enumerate(zip(template_cols, df.columns)):
            if expected != actual:
                print(f"❌ 第{i+1}列不匹配: 期望'{expected}', 实际'{actual}'")
                return False
        
        # 创建临时文件测试导出
        temp_export = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        temp_export.close()
        
        # 导出数据
        df.to_excel(temp_export.name, index=False)
        
        # 读取导出的文件验证
        exported_df = pd.read_excel(temp_export.name)
        exported_cols = list(exported_df.columns)
        
        if exported_cols == template_cols:
            print("✅ 导出文件列名与模板完全一致！")
        else:
            print("❌ 导出文件列名与模板不一致")
            print(f"期望: {template_cols}")
            print(f"导出: {exported_cols}")
            return False
        
        print("✅ 所有测试通过！")
        print("- 下载数据的列名与模板完全一致")
        print("- 列的顺序与模板完全一致")
        print("- 导出的Excel文件格式正确")
        
        # 显示前几行数据作为示例
        print("\n导出数据示例:")
        print(exported_df.head())
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_db.name)
            if 'temp_export' in locals():
                os.unlink(temp_export.name)
        except:
            pass


if __name__ == "__main__":
    test_download_format_consistency()
